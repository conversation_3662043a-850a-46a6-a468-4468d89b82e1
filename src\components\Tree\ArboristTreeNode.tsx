import React, { use<PERSON><PERSON>back, useContext, useEffect, useMemo, useRef, useState } from 'react';
import { NodeRendererProps, Tree } from 'react-arborist';
import { ComponentDetails, TreeNodeType } from '@/utils/types';
import TreeHeader from '../Tree/TreeHeader/TreeNodeHeader';
import { useTreeWidth } from '@/hooks/useTreeHeight';

import { useQueryClient } from '@tanstack/react-query';
import { fetchComponentDetails, fetchTreeNodes, fetchVersionDetails } from '@/services/api/index';
import { useDebounce } from '@/hooks/utils/useDebounce';
import { TreeContext } from '@/context/TreeContext';
import { useTreeNode } from '@/hooks/useTreeNode';

interface ArboristTreeNodeProps {
  data: TreeNodeType[];
  searchTerm: string;
  path: string[];
  onComponentSelect: (details: ComponentDetails | null, path: string[]) => void;
  calculatedHeight?: number;
}

interface ArboristData extends Omit<TreeNodeType, 'children'> {
  children?: ArboristData[];
  isLoading?: boolean;
  hasMoreChildren?: boolean;
  currentPage?: number;
  totalCount?: number;
  path: string[];
}

interface TreeNodeRendererProps extends NodeRendererProps<ArboristData> {
  updateNodeInTree: (node: ArboristData) => void;
  selectedNodeId: string | null;
  onComponentSelect: (details: ComponentDetails | null, path: string[]) => void;
}

/**
 * Optimized TreeNodeRenderer component
 */
const TreeNodeRenderer = React.memo(
  ({
    node,
    style,
    dragHandle,
    updateNodeInTree,
    selectedNodeId,
    onComponentSelect,
  }: TreeNodeRendererProps) => {
    const queryClient = useQueryClient();
    const treeContext = useContext(TreeContext);
    const { isExpanded, toggleExpand } = useTreeNode(node.id);
    const isUserClickRef = useRef(false);

    // Get prefetching configuration from context or use defaults
    const { prefetchEnabled = true, prefetchDelay = 500 } = treeContext || {};
    const hoverTimerRef = useRef<number | null>(null);
    const [isPrefetching, setIsPrefetching] = useState(false);

    useEffect(() => {
      if (node.isOpen !== isExpanded) {
        node.toggle();
      }
    }, [node, isExpanded]);

    const loadNodeData = useCallback(async () => {
      try {
        if (node.data.type === 'Directory') {
          const firstPageResult = await queryClient.fetchQuery({
            queryKey: ['directoryChildren', node.data.id, 1],
            queryFn: () => fetchTreeNodes(node.data.id, 1, 50),
          });

          updateNodeInTree({
            ...node.data,
            children: firstPageResult.nodes.map(child => ({
              ...child,
              path: [...node.data.path, child.name],
            })),
            isLoading: false,
            hasMoreChildren: firstPageResult.hasMorePages,
            currentPage: 1,
            totalCount: firstPageResult.totalCount,
          });

          if (treeContext) {
            treeContext.expandNode(node.id);
          }
        } else if (node.data.type === 'Component') {
          const [details, versions] = await Promise.all([
            queryClient.fetchQuery({
              queryKey: ['componentDetails', node.data.id],
              queryFn: () => fetchComponentDetails(node.data.id),
            }),
            queryClient.fetchQuery({
              queryKey: ['versionDetails', node.data.id],
              queryFn: () => fetchVersionDetails(node.data.id),
            }),
          ]);

          updateNodeInTree({ ...node.data, isLoading: false });

          if (treeContext) {
            treeContext.expandNode(node.id);
          }

          if (isUserClickRef.current && details && versions) {
            const versionsArray = versions.versions || [];
            const newVersionDetails = {
              ...details,
              versions: versionsArray,
              localVersion: null,
              backupJobConfigured: false,
            };
            onComponentSelect(newVersionDetails, node.data.path);
            isUserClickRef.current = false;
          }
        }
      } catch (error) {
        updateNodeInTree({ ...node.data, isLoading: false });
      }
    }, [
      node.data,
      node.id,
      queryClient,
      updateNodeInTree,
      onComponentSelect,
      isUserClickRef,
      treeContext,
    ]);

    useEffect(() => {
      if (node.data.isLoading) {
        loadNodeData();
      }
    }, [node.data.isLoading, loadNodeData]);

    const handleToggle = useCallback(() => {
      const isComponent = node.data.type === 'Component';

      if (isComponent) {
        updateNodeInTree({ ...node.data, isLoading: true });
        isUserClickRef.current = true;
      } else {
        // It's a Directory
        if (!isExpanded && !node.data.children?.length) {
          // If it's collapsed AND has no children, it needs to load data.
          // Loading data will also trigger expansion via treeContext.expandNode in loadNodeData.
          updateNodeInTree({ ...node.data, isLoading: true });
          isUserClickRef.current = true;
        } else {
          // If it's a directory that is already expanded (isExpanded is true),
          // or it's collapsed (!isExpanded) but already has children,
          // then we just need to toggle its expansion state in our context.
          // The useEffect will take care of calling node.toggle() for react-arborist.
          toggleExpand();
        }
      }
    }, [node, isExpanded, updateNodeInTree, toggleExpand]);

    const handleMouseEnter = useCallback(() => {
      if (!prefetchEnabled || isPrefetching) return;

      if (node.data.type === 'Component') {
        const hasComponentData = queryClient.getQueryData(['componentDetails', node.data.id]);
        if (hasComponentData) return; // Don't prefetch if data already exists
      } else if (node.data.type === 'Directory') {
        // Check if the first page of directory children already exists in cache
        const hasDirectoryData = queryClient.getQueryData(['directoryChildren', node.data.id, 1]);
        if (hasDirectoryData) return; // Don't prefetch if data already exists
      }

      hoverTimerRef.current = window.setTimeout(() => {
        setIsPrefetching(true);

        if (node.data.type === 'Component') {
          queryClient
            .prefetchQuery({
              queryKey: ['componentDetails', node.data.id],
              queryFn: () => fetchComponentDetails(node.data.id),
            })
            .catch(() => {}); // Handle or log prefetch error if necessary

          queryClient
            .prefetchQuery({
              queryKey: ['versionDetails', node.data.id],
              queryFn: () => fetchVersionDetails(node.data.id),
            })
            .catch(() => {}); // Handle or log prefetch error if necessary
        } else if (node.data.type === 'Directory') {
          queryClient
            .prefetchQuery({
              queryKey: ['directoryChildren', node.data.id, 1], // Prefetch first page
              queryFn: () => fetchTreeNodes(node.data.id, 1, 50),
            })
            .catch(() => {}); // Handle or log prefetch error if necessary
        }

        setTimeout(() => setIsPrefetching(false), 1000);
      }, prefetchDelay);
    }, [node.data, queryClient, isPrefetching, prefetchEnabled, prefetchDelay]);

    const handleMouseLeave = useCallback(() => {
      if (!prefetchEnabled) return;

      if (hoverTimerRef.current) {
        clearTimeout(hoverTimerRef.current);
        hoverTimerRef.current = null;
      }
      // Reset prefetching state only if it was actively prefetching or about to.
      // This prevents resetting if mouse leaves after prefetch completed.
      // However, for simplicity and to ensure clean state on mouse re-enter, resetting here is fine.
      setIsPrefetching(false);
    }, [prefetchEnabled]);

    // Cleanup timer on unmount
    useEffect(() => {
      return () => {
        if (hoverTimerRef.current) {
          clearTimeout(hoverTimerRef.current);
        }
      };
    }, []);

    const isSelected = node.id === selectedNodeId && node.data.type === 'Component';

    return (
      <div
        style={{
          ...style,
          width: '100%',
          height: '48px',
          display: 'flex',
          alignItems: 'center',
          position: 'relative',
        }}
        ref={dragHandle}
        className={isSelected ? 'bg-secondary-background' : 'hover:bg-secondary-background'}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleToggle}
        role="button"
        aria-expanded={isExpanded}
      >
        {/* Blue collar indicator for selected nodes - positioned at the very start */}
        {isSelected && <div className="tree-node-selected-indicator" />}

        <TreeHeader
          expanded={isExpanded}
          node={node.data}
          toggleExpand={handleToggle}
          isLoading={node.data.isLoading}
        />
      </div>
    );
  },
  (prevProps, nextProps) => {
    return (
      prevProps.node.id === nextProps.node.id &&
      prevProps.node.isOpen === nextProps.node.isOpen &&
      prevProps.node.data.isLoading === nextProps.node.data.isLoading &&
      prevProps.selectedNodeId === nextProps.selectedNodeId &&
      prevProps.style.paddingLeft === nextProps.style.paddingLeft
    );
  }
);

const ArboristTreeNode: React.FC<ArboristTreeNodeProps> = ({
  data,
  onComponentSelect,
  searchTerm = '',
  calculatedHeight,
}) => {
  const treeContext = useContext(TreeContext);
  const isLoadingRef = useRef(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const lastLoadTime = useRef(0);
  const LOAD_THROTTLE_MS = 200; // Reduced from 500ms to 200ms for faster response
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Track directories that can load more for debugging
  const expandedDirectoriesRef = useRef<Set<string>>(new Set());

  // Essential performance tracking
  const performanceRef = useRef({
    loadAttempts: 0,
    successfulLoads: 0,
    lastLoadTime: 0,
  });
  const lastSelectedNodeRef = useRef<{
    details: ComponentDetails | null;
    path: string[];
  } | null>(null);

  const debouncedSearchTerm = useDebounce(searchTerm, 500);
  const searchTermLower = useMemo(
    () => debouncedSearchTerm?.toLowerCase() ?? '',
    [debouncedSearchTerm]
  );

  const treeData = useMemo<ArboristData[]>(() => {
    return data.map(
      (node): ArboristData => ({
        ...node,
        path: [node.name],
        children: node.children
          ? node.children.map(
              (child): ArboristData => ({
                ...child,
                path: [node.name, child.name],
                children: undefined,
              })
            )
          : undefined,
      })
    );
  }, [data]);

  const [internalTreeData, setInternalTreeData] = useState<ArboristData[]>(treeData);
  const selectedNodeIdRef = useRef<string | null>(null);
  const treeRef = useRef<any>(null);
  const queryClient = useQueryClient();

  useEffect(() => {
    if (treeData && treeData.length > 0) {
      // Don't automatically expand nodes - let user control expansion
      setInternalTreeData(treeData);
    }
  }, [treeData]);

  const filterTree = useCallback(
    (nodes: ArboristData[]): ArboristData[] => {
      if (!searchTermLower) return nodes;
      if (!nodes?.length) return [];

      const result: ArboristData[] = [];

      for (const node of nodes) {
        const nodeMatches = node.name.toLowerCase().includes(searchTermLower);
        let filteredChildren: ArboristData[] = [];

        if (nodeMatches && !node.children?.length) {
          result.push(node);
          continue;
        }

        if (node.children?.length) {
          filteredChildren = filterTree(node.children);
        }

        if (nodeMatches || filteredChildren.length) {
          result.push({
            ...node,
            children: filteredChildren,
          });
        }
      }

      return result;
    },
    [searchTermLower]
  );

  const filteredTreeData = useMemo(
    () => (searchTermLower ? filterTree(internalTreeData) : internalTreeData),
    [internalTreeData, filterTree, searchTermLower]
  );

  const updateNodeInTree = useCallback((updatedNode: ArboristData) => {
    setInternalTreeData(prev => {
      if (!prev.length) return prev;

      const updateNode = (nodes: ArboristData[]): ArboristData[] => {
        return nodes.map(node => {
          if (node.id === updatedNode.id) {
            return { ...node, ...updatedNode };
          }

          if (node.children?.length) {
            return {
              ...node,
              children: updateNode(node.children),
            };
          }

          return node;
        });
      };

      return updateNode(prev);
    });
  }, []);

  const handleComponentSelect = useCallback(
    (details: ComponentDetails | null, path: string[], nodeId: string) => {
      if (details && nodeId) {
        selectedNodeIdRef.current = nodeId;
        lastSelectedNodeRef.current = { details, path };
      }
      onComponentSelect(details, path);
    },
    [onComponentSelect]
  );

  const renderNode = useCallback(
    (props: NodeRendererProps<ArboristData>) => (
      <TreeNodeRenderer
        {...props}
        updateNodeInTree={updateNodeInTree}
        selectedNodeId={selectedNodeIdRef.current}
        onComponentSelect={(details, path) => handleComponentSelect(details, path, props.node.id)}
      />
    ),
    [updateNodeInTree, handleComponentSelect]
  );

  useEffect(() => {
    if (!searchTermLower && lastSelectedNodeRef.current) {
      const { details, path } = lastSelectedNodeRef.current;
      onComponentSelect(details, path);
    }
  }, [searchTermLower, onComponentSelect]);

  // Use calculated height from parent (TreeView component uses useTreeHeight hook)
  const treeHeight = calculatedHeight || 600; // Fallback if no height provided

  // Use the useTreeWidth hook for dynamic width calculation
  const treeWidth = useTreeWidth(scrollContainerRef);

  const findLastNodeThatCanLoadMore = useCallback(() => {
    if (!treeRef.current) {
      return null;
    }

    const visibleNodes = treeRef.current.visibleNodes || [];
    if (visibleNodes.length === 0) {
      return null;
    }

    // Find all expanded directories that can load more, starting from the bottom
    const candidateNodes = [];

    // Iterate backwards for efficiency (most likely candidates are at the bottom)
    for (let i = visibleNodes.length - 1; i >= 0; i--) {
      const node = visibleNodes[i];

      if (node.data.type !== 'Directory') {
        continue;
      }

      if (!node.isOpen) {
        continue;
      }

      const canLoadMore =
        node.data.hasMoreChildren === true && !node.data.isLoading && !node.data.isLoadingMore;

      if (canLoadMore) {
        // Find the last child of this directory in visible nodes
        let lastChildIndex = i;
        for (let j = i + 1; j < visibleNodes.length; j++) {
          const potentialChild = visibleNodes[j];
          // Check if this node is a child of our directory by comparing paths
          if (
            potentialChild.data.path &&
            node.data.path &&
            potentialChild.data.path.length > node.data.path.length &&
            potentialChild.data.path.slice(0, node.data.path.length).join('/') ===
              node.data.path.join('/')
          ) {
            lastChildIndex = j;
          } else {
            break; // No longer a child of this directory
          }
        }

        candidateNodes.push({
          node: node.data,
          treeNode: node,
          lastChildIndex,
          directoryIndex: i,
        });
      }
    }

    // Return the directory whose last child is closest to the bottom of visible area
    if (candidateNodes.length === 0) {
      return null;
    }

    // Sort by lastChildIndex descending to get the directory with children closest to bottom
    candidateNodes.sort((a, b) => b.lastChildIndex - a.lastChildIndex);

    return candidateNodes[0];
  }, []);

  // Track active abort controllers for cleanup
  const activeAbortControllersRef = useRef<Set<AbortController>>(new Set());
  const isMountedRef = useRef(true);

  // Component unmount cleanup
  useEffect(() => {
    isMountedRef.current = true;
    return () => {
      isMountedRef.current = false;
      // Abort all active requests on unmount
      activeAbortControllersRef.current.forEach(controller => {
        controller.abort();
      });
      activeAbortControllersRef.current.clear();
    };
  }, []);

  const loadMoreItems = useCallback(async () => {
    const startTime = Date.now();

    // Atomic check and set for loading state
    if (isLoadingRef.current || isLoadingMore) {
      return;
    }

    const timeSinceLastLoad = startTime - lastLoadTime.current;

    if (timeSinceLastLoad < LOAD_THROTTLE_MS) {
      return;
    }

    const result = findLastNodeThatCanLoadMore();

    if (!result) {
      return;
    }

    const { node: nodeToLoadMore } = result;

    // Create abort controller for this request
    const abortController = new AbortController();
    activeAbortControllersRef.current.add(abortController);

    // Track this directory
    expandedDirectoriesRef.current.add(nodeToLoadMore.id);

    // Atomic state updates
    isLoadingRef.current = true;
    lastLoadTime.current = startTime;
    setIsLoadingMore(true);

    // Check if component is still mounted before updating
    if (!isMountedRef.current) {
      abortController.abort();
      activeAbortControllersRef.current.delete(abortController);
      return;
    }

    updateNodeInTree({
      ...nodeToLoadMore,
      isLoadingMore: true,
    });

    try {
      const currentPage = nodeToLoadMore.currentPage || 1;
      const nextPage = currentPage + 1;

      const response = await queryClient.fetchQuery({
        queryKey: ['directoryChildren', nodeToLoadMore.id, nextPage],
        queryFn: ({ signal }) => fetchTreeNodes(nodeToLoadMore.id, nextPage, 50),
        signal: abortController.signal,
      });

      // Check if request was aborted or component unmounted
      if (abortController.signal.aborted || !isMountedRef.current) {
        return;
      }

      const updatedChildren = [
        ...(nodeToLoadMore.children || []),
        ...response.nodes.map(node => ({
          ...node,
          path: [...nodeToLoadMore.path, node.name],
        })),
      ];

      performanceRef.current.successfulLoads++;
      performanceRef.current.lastLoadTime = Date.now();

      // Final check before updating state
      if (isMountedRef.current) {
        updateNodeInTree({
          ...nodeToLoadMore,
          children: updatedChildren,
          hasMoreChildren: response.hasMorePages,
          currentPage: nextPage,
          isLoadingMore: false,
        });
      }
    } catch (error) {
      // Don't log errors for aborted requests
      if (!abortController.signal.aborted) {
        console.error('[InfiniteScroll] Error loading more items:', {
          nodeId: nodeToLoadMore.id,
          error: error instanceof Error ? error.message : String(error),
        });

        // Only update state if component is still mounted
        if (isMountedRef.current) {
          updateNodeInTree({
            ...nodeToLoadMore,
            isLoadingMore: false,
          });

          // Retry network errors after a delay (with new abort controller)
          if (error instanceof Error && error.message.includes('Network')) {
            setTimeout(() => {
              if (isMountedRef.current) {
                loadMoreItemsRef.current().catch(console.error);
              }
            }, 2000);
          }
        }
      }
    } finally {
      // Clean up abort controller
      activeAbortControllersRef.current.delete(abortController);

      // Reset loading states only if component is mounted
      if (isMountedRef.current) {
        isLoadingRef.current = false;
        setIsLoadingMore(false);
        lastLoadTime.current = Date.now();
      }
    }
  }, [findLastNodeThatCanLoadMore, isLoadingMore, queryClient, updateNodeInTree]);

  // Stable reference to loadMoreItems to avoid event listener re-attachment
  const loadMoreItemsRef = useRef(loadMoreItems);
  loadMoreItemsRef.current = loadMoreItems;

  // RAF-based monitoring for very fast scrolling
  const startRAFMonitoring = useCallback(() => {
    if (rafIdRef.current) {
      return; // Already monitoring
    }

    const monitorScroll = () => {
      const treeInstance = treeRef.current;
      if (!treeInstance?.listEl?.current || !isMonitoringRef.current) {
        isMonitoringRef.current = false;
        rafIdRef.current = null;
        return;
      }

      const treeScrollElement = treeInstance.listEl.current as HTMLElement;
      const { scrollTop, scrollHeight, clientHeight } = treeScrollElement;
      const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

      // Very aggressive check for fast scrolling - if we're close to bottom, load immediately
      if (distanceFromBottom < 100 && !isLoadingRef.current) {
        console.log('[InfiniteScroll] RAF detected near bottom, triggering load:', {
          distanceFromBottom,
          scrollTop,
          scrollHeight,
          clientHeight
        });

        if (loadMoreItemsRef.current) {
          loadMoreItemsRef.current().catch(console.error);
        }
      }

      // Continue monitoring if still scrolling fast or near bottom
      if (distanceFromBottom < 300) {
        rafIdRef.current = requestAnimationFrame(monitorScroll);
      } else {
        // Stop monitoring after a delay
        setTimeout(() => {
          isMonitoringRef.current = false;
          rafIdRef.current = null;
        }, 1000);
      }
    };

    rafIdRef.current = requestAnimationFrame(monitorScroll);
  }, []);

  // Intersection Observer for more reliable scroll detection
  const sentinelRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  useEffect(() => {
    const treeInstance = treeRef.current;

    if (
      treeInstance &&
      treeInstance.listEl &&
      treeInstance.listEl.current &&
      typeof treeInstance.listEl.current.addEventListener === 'function'
    ) {
      const treeScrollElement = treeInstance.listEl.current as HTMLElement;

      let ticking = false;
      let lastScrollTime = 0;
      const SCROLL_DEBOUNCE_MS = 50; // Reduced from 100ms to 50ms for faster response

      const handleInnerScroll = () => {
        const now = Date.now();

        // Mark that user has scrolled (enables intersection observer)
        // Use ref to prevent stale closure issues
        if (!hasUserScrolledRef.current) {
          hasUserScrolledRef.current = true;
          setHasUserScrolled(true);
        }

        // Start RAF monitoring for fast scroll detection
        if (!isMonitoringRef.current) {
          isMonitoringRef.current = true;
          startRAFMonitoring();
        }

        if (!treeScrollElement) {
          return;
        }

        // Check for emergency bottom case first (no debouncing for critical situations)
        const { scrollTop, scrollHeight, clientHeight } = treeScrollElement;
        const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

        // Emergency case: very close to bottom - bypass all throttling
        if (distanceFromBottom < 50 && !isLoadingRef.current) {
          console.log('[InfiniteScroll] Emergency bottom detection, immediate load:', {
            distanceFromBottom,
            scrollTop,
            scrollHeight,
            clientHeight
          });

          if (loadMoreItemsRef.current && isMountedRef.current) {
            loadMoreItemsRef.current().catch(console.error);
          }
          return;
        }

        // Normal case: apply debouncing for regular scroll handling
        if (isLoadingRef.current) {
          return;
        }

        if (now - lastScrollTime < SCROLL_DEBOUNCE_MS) {
          return;
        }
        lastScrollTime = now;

        // Re-get scroll measurements for velocity calculation (may have changed)
        const currentScrollTop = treeScrollElement.scrollTop;
        const currentScrollHeight = treeScrollElement.scrollHeight;
        const currentClientHeight = treeScrollElement.clientHeight;
        const currentDistanceFromBottom = currentScrollHeight - currentScrollTop - currentClientHeight;

        // Calculate scroll velocity for adaptive threshold
        const scrollVelocity = scrollVelocityRef.current;
        const timeDelta = now - scrollVelocity.lastScrollTime;
        const scrollDelta = Math.abs(currentScrollTop - scrollVelocity.lastScrollTop);

        if (timeDelta > 0) {
          scrollVelocity.velocity = scrollDelta / timeDelta; // pixels per millisecond
        }

        scrollVelocity.lastScrollTop = currentScrollTop;
        scrollVelocity.lastScrollTime = now;

        // Adaptive threshold based on scroll velocity
        // Fast scrolling (>2 px/ms) gets larger threshold for earlier loading
        const baseThreshold = 500;
        const velocityMultiplier = Math.min(scrollVelocity.velocity * 200, 1000); // Cap at 1000px
        const adaptiveThreshold = baseThreshold + velocityMultiplier;

        // Primary trigger: adaptive threshold based on scroll velocity
        const shouldLoadPrimary = currentDistanceFromBottom < adaptiveThreshold;

        // Backup trigger: user reached very bottom (within 100px) - but we already handled <50px above
        const shouldLoadBackup = currentDistanceFromBottom < 100 && currentDistanceFromBottom >= 50 && !isLoadingRef.current;

        if (shouldLoadPrimary || shouldLoadBackup) {
          if (!ticking) {
            performanceRef.current.loadAttempts++;

            // Debug logging for fast scroll detection
            if (scrollVelocity.velocity > 2) {
              console.log('[InfiniteScroll] Fast scroll detected:', {
                velocity: scrollVelocity.velocity.toFixed(2),
                distanceFromBottom: currentDistanceFromBottom,
                adaptiveThreshold: adaptiveThreshold.toFixed(0),
                triggerType: shouldLoadBackup ? 'backup' : 'primary',
              });
            }

            // Use immediate execution for backup trigger (very bottom), faster timeout for primary
            const timeoutDelay = shouldLoadBackup ? 0 : 25;

            const timeoutId = setTimeout(() => {
              activeTimeoutsRef.current.delete(timeoutId);

              if (!isLoadingRef.current && loadMoreItemsRef.current && isMountedRef.current) {
                loadMoreItemsRef.current().catch(console.error);
              }
              ticking = false;
            }, timeoutDelay);

            // Track timeout for cleanup
            activeTimeoutsRef.current.add(timeoutId);
            ticking = true;
          }
        }

        // Scroll end detection - trigger loading when scrolling stops near bottom
        if (scrollEndTimeoutRef.current) {
          clearTimeout(scrollEndTimeoutRef.current);
        }

        scrollEndTimeoutRef.current = setTimeout(() => {
          if (!treeScrollElement || isLoadingRef.current) return;

          const { scrollTop, scrollHeight, clientHeight } = treeScrollElement;
          const finalDistanceFromBottom = scrollHeight - scrollTop - clientHeight;

          // If user stopped scrolling near the bottom, trigger loading
          if (finalDistanceFromBottom < 200) {
            console.log('[InfiniteScroll] Scroll ended near bottom, triggering load:', {
              distanceFromBottom: finalDistanceFromBottom,
              scrollTop,
              scrollHeight,
              clientHeight
            });

            if (loadMoreItemsRef.current && isMountedRef.current) {
              loadMoreItemsRef.current().catch(console.error);
            }
          }
        }, 150); // Wait 150ms after scroll stops
      };

      treeScrollElement.addEventListener('scroll', handleInnerScroll, { passive: true });

      return () => {
        if (treeScrollElement && typeof treeScrollElement.removeEventListener === 'function') {
          treeScrollElement.removeEventListener('scroll', handleInnerScroll);
        }

        // Cleanup RAF monitoring
        if (rafIdRef.current) {
          cancelAnimationFrame(rafIdRef.current);
          rafIdRef.current = null;
        }
        isMonitoringRef.current = false;

        // Cleanup scroll end timeout
        if (scrollEndTimeoutRef.current) {
          clearTimeout(scrollEndTimeoutRef.current);
          scrollEndTimeoutRef.current = null;
        }
      };
    }
  }, [treeRef.current]); // Depend on treeRef.current to re-attach when tree instance changes

  // Smart Intersection Observer - only activates after user has scrolled
  const [hasUserScrolled, setHasUserScrolled] = useState(false);
  const hasUserScrolledRef = useRef(false);
  const activeTimeoutsRef = useRef<Set<NodeJS.Timeout>>(new Set());

  // Velocity tracking for fast scroll detection
  const scrollVelocityRef = useRef({ lastScrollTop: 0, lastScrollTime: 0, velocity: 0 });

  // RAF-based scroll monitoring for very fast scrolling
  const rafIdRef = useRef<number | null>(null);
  const isMonitoringRef = useRef(false);

  // Scroll end detection
  const scrollEndTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Sync state with ref to prevent stale closures
  useEffect(() => {
    hasUserScrolledRef.current = hasUserScrolled;
  }, [hasUserScrolled]);

  // Stable intersection observer setup - only recreates when hasUserScrolled changes
  useEffect(() => {
    if (!sentinelRef.current || !hasUserScrolled) {
      return;
    }

    const sentinel = sentinelRef.current;

    const observer = new IntersectionObserver(
      entries => {
        const entry = entries[0];

        if (entry.isIntersecting && !isLoadingRef.current) {
          // Reduced timeout from 100ms to 50ms for faster response
          const timeoutId = setTimeout(() => {
            activeTimeoutsRef.current.delete(timeoutId);

            if (!isLoadingRef.current && loadMoreItemsRef.current) {
              loadMoreItemsRef.current().catch(console.error);
            }
          }, 50);

          // Track timeout for cleanup
          activeTimeoutsRef.current.add(timeoutId);
        }
      },
      {
        root: null,
        rootMargin: '200px', // Increased from 100px to 200px for earlier detection
        threshold: 0.05, // Reduced from 0.1 to 0.05 for more sensitive detection
      }
    );

    observer.observe(sentinel);
    observerRef.current = observer;

    return () => {
      observer.disconnect();
      observerRef.current = null;

      // Clear any pending timeouts
      activeTimeoutsRef.current.forEach(timeoutId => {
        clearTimeout(timeoutId);
      });
      activeTimeoutsRef.current.clear();
    };
  }, [hasUserScrolled]); // Only recreate when hasUserScrolled changes

  // Final cleanup effect - ensures all resources are cleaned up on unmount
  useEffect(() => {
    return () => {
      // Mark component as unmounted
      isMountedRef.current = false;

      // Abort all active requests
      activeAbortControllersRef.current.forEach(controller => {
        controller.abort();
      });
      activeAbortControllersRef.current.clear();

      // Clear all active timeouts
      activeTimeoutsRef.current.forEach(timeoutId => {
        clearTimeout(timeoutId);
      });
      activeTimeoutsRef.current.clear();

      // Disconnect intersection observer
      if (observerRef.current) {
        observerRef.current.disconnect();
        observerRef.current = null;
      }

      // Cleanup RAF monitoring
      if (rafIdRef.current) {
        cancelAnimationFrame(rafIdRef.current);
        rafIdRef.current = null;
      }
      isMonitoringRef.current = false;

      // Cleanup scroll end timeout
      if (scrollEndTimeoutRef.current) {
        clearTimeout(scrollEndTimeoutRef.current);
        scrollEndTimeoutRef.current = null;
      }

      // Reset loading states
      isLoadingRef.current = false;
    };
  }, []);

  const treeComponent = useMemo(() => {
    // Show tree skeleton when loading AND no data (initial load) OR when we have empty data
    const isLoading = treeContext?.loading || false;
    const hasNoData = !filteredTreeData || filteredTreeData.length === 0;
    const isInitialLoad = isLoading && hasNoData;

    // Show simple spinner during initial load (loading + no data)
    // This covers the case where data is [] but we're still loading
    if (isInitialLoad) {
      return (
        <div className="relative" ref={scrollContainerRef} style={{ height: `${treeHeight}px` }}>
          <div className="absolute inset-0 flex-center">
            <div className="text-center">
              <div className="spinner-primary spinner-lg mb-2" />
              <p className="text-border-color-dark text-sm">Loading tree data...</p>
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="relative" ref={scrollContainerRef} style={{ height: `${treeHeight}px` }}>
        <Tree<ArboristData>
          ref={treeRef}
          data={filteredTreeData}
          width={treeWidth} // Dynamically calculated: 420px container - 28px reserved space = 392px max
          height={treeHeight}
          indent={15}
          rowHeight={48}
          overscanCount={40} // Increased from 20 to 40 for better fast scroll handling
          disableDrag={true}
          disableDrop={true}
          className="tree-scroll-container"
        >
          {renderNode}
        </Tree>

        {/* Smart Intersection Observer sentinel - only active after user scrolls */}
        <div
          ref={sentinelRef}
          className="absolute bottom-0 left-0 w-full h-1 pointer-events-none"
          style={{ transform: 'translateY(-100px)' }} // Increased back to -100px for earlier detection
        />

        {isLoadingMore && (
          <div className="absolute bottom-0 left-0 right-0 flex-center py-2 bg-background">
            <div className="spinner-primary spinner-md" />
          </div>
        )}
      </div>
    );
  }, [filteredTreeData, treeHeight, treeWidth, renderNode, isLoadingMore]);

  return <div className="h-full">{treeComponent}</div>;
};

export default React.memo(ArboristTreeNode);
